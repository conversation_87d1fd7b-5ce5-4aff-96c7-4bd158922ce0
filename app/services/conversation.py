import logging
from typing import cast
from uuid import UUID

from constants.message import (
    WELCOME_MESSAGE,
    WELCOME_MESSAGE_WITH_MANY_DASH_TASKS,
    MessageRole,
    MessageType,
)
from exceptions import EntityNotFoundError
from repositories import ConversationRepository
from schemas import (
    ConversationCreationRequest,
    ConversationResponse,
    ConversationWithWelcomeMessageResponse,
    KXDashTaskOption,
    MessageValidator,
    SystemMessageSerializer,
)

from .conversation_message import ConversationMessageService
from .document import DocumentService
from .extracted_data import ExtractedDataService
from .kx_dash import KXDashService


__all__ = ['ConversationService']


logger = logging.getLogger(__name__)


class ConversationService:
    """Service for conversation-related business logic."""

    def __init__(
        self,
        conversation_repository: ConversationRepository,
        conversation_message_service: ConversationMessageService,
        extracted_data_service: ExtractedDataService,
        document_service: DocumentService,
        kx_dash_service: KXDashService,
    ):
        self.conversation_repository = conversation_repository
        self.conversation_message_service = conversation_message_service
        self.extracted_data_service = extracted_data_service
        self.document_service = document_service
        self.kx_dash_service = kx_dash_service

    async def create(
        self, conversation_data: ConversationCreationRequest, user_id: UUID, user_name: str
    ) -> ConversationResponse:
        """
        Create a new conversation.

        Args:
            conversation_data: Data for creating the conversation

        Returns:
            Response with the created conversation data

        Raises:
            DatabaseException: If there's an error creating the conversation
        """
        try:
            logger.debug('Creating a new conversation')
            conversation = await self.conversation_repository.create(
                conversation_data=conversation_data, user_id=user_id, user_name=user_name
            )
            return ConversationResponse.model_validate(conversation, from_attributes=True)
        except Exception as e:  # pragma: no cover
            logger.error('Error creating conversation: %s', e)
            raise

    async def create_with_welcome_message(
        self, conversation_data: ConversationCreationRequest, user_id: UUID, user_name: str
    ) -> ConversationWithWelcomeMessageResponse:
        """
        Create a new conversation with a welcome message.

        Args:
            conversation_data: Data for creating the conversation

        Returns:
            Response with the created conversation data

        Raises:
            DatabaseException: If there's an error creating the conversation
        """
        try:
            conversation = await self.create(conversation_data, user_id, user_name)

            welcome_message = await self._create_welcome_message(conversation.id)

            return ConversationWithWelcomeMessageResponse(
                conversation=conversation,
                welcome_message=cast(SystemMessageSerializer, welcome_message),
            )
        except Exception as e:  # pragma: no cover
            logger.error('Error creating conversation with welcome message: %s', e)
            raise

    async def _create_welcome_message(self, conversation_id: UUID) -> SystemMessageSerializer:
        """
        Create appropriate welcome message based on available dash tasks.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            SystemMessageSerializer with the welcome message

        Raises:
            Exception: If there's an error creating the welcome message
        """
        kx_dash_tasks = await self.kx_dash_service.list()

        if not kx_dash_tasks:
            message = await self.conversation_message_service.create_message(
                MessageValidator(
                    conversation_id=conversation_id,
                    role=MessageRole.SYSTEM,
                    type=MessageType.TEXT,
                    content=WELCOME_MESSAGE,
                ),
            )
            return cast(SystemMessageSerializer, message)

        options = tuple(
            KXDashTaskOption(
                activity_id=task.activity_id,
                client_name=task.client_name,
                engagement_code=task.engagement_code,
            )
            for task in kx_dash_tasks
        )

        if len(options) == 1:
            welcome_message_data = await self.kx_dash_service.on_select(
                options[0], conversation_id, is_welcome_message=True
            )
            message = await self.conversation_message_service.create_message(welcome_message_data)
            return cast(SystemMessageSerializer, message)
        else:
            message = await self.conversation_message_service.create_message(
                MessageValidator(
                    conversation_id=conversation_id,
                    role=MessageRole.SYSTEM,
                    type=MessageType.TEXT,
                    content=WELCOME_MESSAGE_WITH_MANY_DASH_TASKS,
                    options=options,
                ),
            )
            return cast(SystemMessageSerializer, message)

    async def get(self, public_id: UUID) -> ConversationResponse:
        """
        Get a conversation by its ID.

        Args:
            public_id: The public ID of the conversation

        Returns:
            The conversation response

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the conversation
        """
        try:
            logger.debug('Retrieving conversation with ID: %s', public_id)
            conversation = await self.conversation_repository.get(public_id)

            if not conversation:  # pragma: no cover
                # NOTE: excluded from coverage sinse it's not reachable in test with current permission implementation
                raise EntityNotFoundError('Conversation', str(public_id))

            return ConversationResponse.model_validate(conversation, from_attributes=True)
        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving conversation: %s', e)
            raise

    async def delete(self, conversation_id: UUID) -> None:
        """
        Delete a conversation by ID.

        Args:
            conversation_id: UUID of the conversation to delete

        Raises:
            EntityNotFoundError: If conversation with the given ID does not exist
        """
        try:
            logger.debug('Deleting conversation with ID: %s', conversation_id)
            await self.document_service.delete_many(conversation_id)
            await self.conversation_message_service.delete_many(conversation_id)
            await self.extracted_data_service.delete_many(conversation_id)
            await self.conversation_repository.delete(conversation_id)
            logger.info('Successfully deleted conversation with ID: %s', conversation_id)
        except EntityNotFoundError:  # pragma: no cover
            # NOTE: excluded from coverage sinse it's not reachable in test with current permission implementation
            logger.warning('Conversation with ID %s not found for deletion', conversation_id)
            raise
        except Exception as e:  # pragma: no cover
            logger.error('Failed to delete conversation %s: %s', conversation_id, e, exc_info=True)
            raise

    async def get_owner_id(self, conversation_id: UUID) -> UUID | None:
        """
        Get an owner ID for the conversation.

        Args:
            conversation_id: The public ID of the conversation

        Returns:
            The owner ID of the conversation

        """
        try:
            logger.debug('Retrieving an owner ID for the conversation with ID: %s', conversation_id)
            return await self.conversation_repository.get_owner_id(conversation_id)

        except Exception:  # pragma: no cover
            logger.exception('Failed to retrieve an owner ID for the conversation with ID "%s"', conversation_id)
            raise
